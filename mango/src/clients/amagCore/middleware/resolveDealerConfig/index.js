/* @flow */
import { getDealerConfig } from './requests';
import type { Identity } from '../../../../modules/request/types';
import { getContext } from '../../../../server/middleware/createContext';
import {
	VOLKSWAGEN_BRAND,
	VOLKSWAGEN_NUTZFAHRZEUGE_BRAND,
	AUDI_BRAND,
	SKODA_BRAND,
	SEAT_BRAND,
	CUPRA_BRAND,
	WEBSITE_BRAND,
	DEALER_BRAND_WEBSITE_IDENTITYTYPE,
	DEALER_WEBSITE_IDENTITYTYPE,
	STOPGO_BRAND,
} from '../constants';
import getDealerLabel from '../getDealerLabel';
import getBrand from '../getBrand';
import getDealerLabelFromConfig from '../getDealerLabelFromConfig';
import { handleUmbrellaDomains } from './handleUmbrellaDomains';

function getIdentity(brand: string, dealerLabel: string): Identity {
	let dealerId: any;
	let identityType: string;

	switch (brand) {
		case VOLKSWAGEN_BRAND:
		case VOLKSWAGEN_NUTZFAHRZEUGE_BRAND:
		case AUDI_BRAND:
		case STOPGO_BRAND:
		case SKODA_BRAND:
		case SEAT_BRAND:
		case CUPRA_BRAND:
			dealerId = [dealerLabel, brand];
			identityType = DEALER_BRAND_WEBSITE_IDENTITYTYPE;
			break;
		default: // assume dealer website
			dealerId = dealerLabel;
			identityType = DEALER_WEBSITE_IDENTITYTYPE;
	}

	return [dealerId, identityType];
}

function getDefaultTags(brand: string, dealerConfig: Object) {
	const { dealer = {} } = dealerConfig || {};
	const serviceTag = { t: 'val', ns: 'service', val: '*' };
	const brandTag = { t: 'val', ns: 'brand', val: brand };
	const dealerBrandTag = { t: 'val', ns: 'brand', val: '*' };
	const dealerTag = { t: 'val', ns: 'dealer', val: dealer['dealer-id'] };
	const dealerWebsiteTag = { t: 'val', ns: 'dealer-website', val: '*' };

	if (brand !== WEBSITE_BRAND) {
		return [serviceTag, brandTag, dealerTag];
	}

	return [serviceTag, dealerBrandTag, dealerWebsiteTag, dealerTag];
}

async function resolveDealerConfig(req: Request, res: Response, next: Function) {
	const context = getContext(res);
	if (!context) {
		console.error('Context is not set on the response. Dealer config cannot be resolved.'); // eslint-disable-line no-console
		next();
		return;
	}

	const config = context.initialState.config;
	const tenant = context.tenant;
	if (!config || !tenant) {
		console.error('No config or no tenant found. Dealer config cannot be resolved.'); // eslint-disable-line no-console
		next();
		return;
	}

	if (handleUmbrellaDomains(req, res, next)) {
		return;
	}

	const brand = getBrand(config);

	const labelFromConfig = getDealerLabelFromConfig(config);
	const dealerLabelConfig = labelFromConfig ? {
		label: labelFromConfig,
		isCustomDomain: false,
		hasCustomDomain: false,
		source: 'fromConfig',
		valid: true,
	} : getDealerLabel(brand, config, req);

	// ensure that we have received a valid config
	if (!dealerLabelConfig.label || !dealerLabelConfig.valid) {
		console.error('Could not extract dealer name from URL. Dealer config cannot be resolved.'); // eslint-disable-line no-console
		next();
		return;
	}

	const { label } = dealerLabelConfig;
	const identity = getIdentity(brand, label);

	const dealerConfig = await getDealerConfig({ tenant: tenant.name }, identity);
	if (!dealerConfig) {
		console.error(`no dealer config found for "${label}"`); // eslint-disable-line no-console
		res
			.status(404)
			.send('This domain has not been configured correctly. Please make sure this domain is assigned to a dealer config.');
		return;
	}

	/* eslint-disable no-param-reassign */
	res.locals.context.initialState.dealerConfig = dealerConfig;
	res.locals.context.initialState.dealerConfig.dealerTag = label;
	res.locals.context.initialState.dealerConfig.isCustomDomain = dealerLabelConfig.isCustomDomain;
	res.locals.context.initialState.dealerConfig.hasCustomDomain =
		dealerLabelConfig.hasCustomDomain;
	res.locals.context.initialState.dealerConfig.isUmbrellaPage = false;
	res.locals.context.initialState.dealerConfig.source = dealerLabelConfig.source;
	res.locals.context.initialState.config.defaultTags = getDefaultTags(brand, dealerConfig);
	res.locals.context.initialState.config.defaultInterestContext = identity;
	/* eslint-enable no-param-reassign */

	next();
}

export default (resolveDealerConfig: SpectraMiddleware);
