/* @flow */
import { UMBRELLA_PAGE_URLS } from './constants';
import { getContext } from '../../../../server/middleware/createContext';

const isUmbrellaDomain = (req: Request, res: Response) => {
	const context = getContext(res);
	const hostUrl = req.get('host');
	return context
		&& context.initialState
		&& context.initialState.config
		&& context.initialState.config.customConfig
		&& context.initialState.config.customConfig.umbrellaDomains
		&& context.initialState.config.customConfig.umbrellaDomains.includes(hostUrl);
};

function handleUmbrellaDomains(
	req: Request, res: Response, next: Function,
): boolean {
	if (
		isUmbrellaDomain(req, res)
		&& UMBRELLA_PAGE_URLS.includes(req.originalUrl)
	) {
		/* eslint-disable no-param-reassign */
		res.locals.context.initialState.dealerConfig = {
			isUmbrellaPage: true,
		};
		/* eslint-enable no-param-reassign */
		next();
		return true;
	}
	return false;
}

export {
	handleUmbrellaDomains,
	isUmbrellaDomain,
};
