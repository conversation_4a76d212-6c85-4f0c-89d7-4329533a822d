/* @flow */
import { getContext } from '../../../../server/middleware/createContext';
import { error, warn } from '../../../../tango/logger';
import { WEBSITE_BRAND } from '../constants';
import getBrand from '../getBrand';
import getRedirectLinksFromBrand from '../getRedirectLinksFromBrand';

function isWebsiteActivated(dealerConfig, brand: string): ?string {
	if (brand === WEBSITE_BRAND) {
		return (
			dealerConfig &&
			dealerConfig['dealer-website'] &&
			dealerConfig['dealer-website'].activated
		);
	}

	return (
		dealerConfig &&
		dealerConfig['dealer-brand-websites'] &&
		dealerConfig['dealer-brand-websites'][brand] &&
		dealerConfig['dealer-brand-websites'][brand].activated
	);
}

function siteValidation(req: Request, res: Response, next: Function) {
	const context = getContext(res);
	if (!context) {
		error('Context is not set on the response. Site is not valid.');
		res.status(404).send('This site does not exist.');
		return;
	}

	const config = context.initialState.config;
	// $FlowFixMe dealerConfig is not defined on the InitialState type because it is amag specific.
	const dealerConfig = context.initialState.dealerConfig;
	if (!config || !dealerConfig) {
		warn('No config or no dealer config found. Site is not valid.');
		res.status(404).send('This site does not exist.');
		return;
	}

	if (dealerConfig.isUmbrellaPage) {
		next();
		return;
	}

	const brand = getBrand(config);

	if (!isWebsiteActivated(dealerConfig, brand)) {
		warn('Site is not activated.');

		const locale = dealerConfig
			? Object.keys(dealerConfig.dealer.config.managed.languages)[0]
			: 'de';

		const linksFromBrand = getRedirectLinksFromBrand(brand, locale);

		if (!linksFromBrand) {
			res.status(404).send('This site does not exist.');
		}

		res.redirect(linksFromBrand);

		return;
	}

	next();
}

export default (siteValidation: SpectraMiddleware);
